import { expect, test, beforeAll, afterAll, describe } from 'vitest';
import { setupElectronTest, cleanupElectronTest, type ElectronTestContext, waitForOperation } from '../helpers/electron-setup';

/**
 * Comprehensive E2E Test for All Obsidian Formatting Syntax
 *
 * This test verifies that all formatting syntax supported by Obsidian
 * is correctly converted to Lexical format and can round-trip properly.
 */

describe("Comprehensive Formatting E2E Test", () => {
  let context: ElectronTestContext;
  const testFilePath = 'articles/comprehensive-formatting-test.md';

  beforeAll(async () => {
    context = await setupElectronTest();
  });

  afterEach(async () => {
    // Clean up test file
    try {
      await context.page.evaluate(async ({ filePath }) => {
        const app = (window as any).app;
        const file = app.vault.getAbstractFileByPath(filePath);
        if (file) {
          await app.vault.delete(file);
        }
      }, { filePath: testFilePath });
    } catch (error) {
      console.log('Cleanup error (expected if file does not exist):', error);
    }

    // Wait for cleanup to complete
    await new Promise(resolve => setTimeout(resolve, 1000));
  });

  afterAll(async () => {
    await cleanupElectronTest(context);
  });

  test("should correctly convert all Obsidian formatting syntax to Lexical", async () => {
    console.log("Testing comprehensive formatting conversion");

    // Create test content with all supported formatting
    const testContent = `---
title: "Comprehensive Formatting Test"
slug: "comprehensive-formatting-test"
status: draft
---

# Heading 1

## Heading 2

### Heading 3

#### Heading 4

##### Heading 5

###### Heading 6

## Text Formatting

Here we test **bold text**, *italic text*, ***bold italic***, ~~strikethrough~~, and \`inline code\`.

We also test _underscore italic_ and __underscore bold__.

## Lists

### Unordered Lists
- Item 1
- Item 2
  - Nested item 1
  - Nested item 2
- Item 3

### Ordered Lists
1. First item
2. Second item
   1. Nested numbered item
   2. Another nested item
3. Third item

## Tables

| Header 1 | Header 2 | Header 3 |
|----------|----------|----------|
| Cell 1   | Cell 2   | Cell 3   |
| Cell 4   | Cell 5   | Cell 6   |

## Callouts

> [!note]
> This is a note callout

> [!info]
> This is an info callout

> [!tip]
> This is a tip callout

> [!warning]
> This is a warning callout

> [!danger]
> This is a danger callout

## Code Blocks

\`\`\`javascript
function hello() {
  console.log("Hello, world!");
}
\`\`\`

\`\`\`python
def hello():
    print("Hello, world!")
\`\`\`

## Links

[External link](https://example.com)

## Blockquotes

> This is a simple blockquote
>
> With multiple lines

## Mixed Formatting

This paragraph contains **bold text with _italic inside_** and some \`inline code\` too.

- List item with **bold** and *italic* text
- Another item with ~~strikethrough~~ and \`code\`

## Complex Scenarios

1. **Bold list item** with _italic text_
2. List item with \`inline code\` and [a link](https://example.com)
3. ~~Strikethrough list item~~ with ***bold italic***

> [!tip]
> Callout with **bold**, *italic*, and \`code\` formatting inside

Final paragraph with all formatting: **bold**, *italic*, ***bold italic***, ~~strikethrough~~, \`inline code\`, and _underscore italic_.`;

    // Create test file
    await context.page.evaluate(async ({ path, content }) => {
      const app = (window as any).app;

      // Ensure articles directory exists
      const articlesDir = 'articles';
      const abstractFile = app.vault.getAbstractFileByPath(articlesDir);
      if (!abstractFile) {
        await app.vault.createFolder(articlesDir);
      }

      await app.vault.create(path, content);
    }, { path: testFilePath, content: testContent });

    await waitForOperation(1000);

    // Test conversion from Obsidian markdown to Lexical format
    const conversionResult = await context.page.evaluate(async ({ filePath }) => {
      const app = (window as any).app;
      const plugin = app.plugins.plugins['ghost-sync'];
      const file = app.vault.getAbstractFileByPath(filePath);

      if (!file || !plugin || !plugin.lexicalParser) {
        return { success: false, error: 'Required components not available' };
      }

      try {
        const content = await app.vault.read(file);

        // Count various formatting elements in original markdown
        const boldCount = (content.match(/\*\*[^*]+\*\*/g) || []).length;
        const italicCount = (content.match(/\*[^*]+\*/g) || []).length + (content.match(/_[^_]+_/g) || []).length;
        const strikethroughCount = (content.match(/~~[^~]+~~/g) || []).length;
        const inlineCodeCount = (content.match(/`[^`]+`/g) || []).length;
        const headingCount = (content.match(/^#{1,6}\s/gm) || []).length;
        const listItemCount = (content.match(/^[\s]*[-*+]\s/gm) || []).length + (content.match(/^[\s]*\d+\.\s/gm) || []).length;
        const tableCount = (content.match(/\|.*\|/g) || []).length;
        const calloutCount = (content.match(/> \[![^\]]+\]/g) || []).length;
        const codeBlockCount = (content.match(/```[\s\S]*?```/g) || []).length;
        const linkCount = (content.match(/\[[^\]]*\]\([^)]*\)/g) || []).length;

        // Convert to Lexical
        const lexicalResult = await plugin.lexicalParser.markdownToLexical(content);

        if (!lexicalResult.success) {
          return { success: false, error: lexicalResult.error };
        }

        const lexicalJson = JSON.stringify(lexicalResult.data);

        // Count formatting elements in Lexical JSON
        const lexicalBoldCount = (lexicalJson.match(/"format":1/g) || []).length;
        const lexicalItalicCount = (lexicalJson.match(/"format":2/g) || []).length;
        const lexicalStrikethroughCount = (lexicalJson.match(/"format":8/g) || []).length;
        const lexicalInlineCodeCount = (lexicalJson.match(/"format":16/g) || []).length;
        const lexicalHeadingCount = (lexicalJson.match(/"type":"extended-heading"/g) || []).length;
        const lexicalTableCount = (lexicalJson.match(/"type":"markdown".*"markdown":".*\|/g) || []).length;
        const lexicalCalloutCount = (lexicalJson.match(/"type":"callout"/g) || []).length;

        // Test round-trip conversion
        const markdownResult = await plugin.lexicalParser.lexicalToMarkdown(lexicalResult.data);

        return {
          success: true,
          original: {
            bold: boldCount,
            italic: italicCount,
            strikethrough: strikethroughCount,
            inlineCode: inlineCodeCount,
            heading: headingCount,
            listItem: listItemCount,
            table: tableCount,
            callout: calloutCount,
            codeBlock: codeBlockCount,
            link: linkCount
          },
          lexical: {
            bold: lexicalBoldCount,
            italic: lexicalItalicCount,
            strikethrough: lexicalStrikethroughCount,
            inlineCode: lexicalInlineCodeCount,
            heading: lexicalHeadingCount,
            table: lexicalTableCount,
            callout: lexicalCalloutCount
          },
          roundTrip: {
            success: markdownResult.success,
            content: markdownResult.data?.substring(0, 500) // First 500 chars for debugging
          }
        };
      } catch (error) {
        return { success: false, error: error.message };
      }
    }, { filePath: testFilePath });

    console.log('Conversion result:', JSON.stringify(conversionResult, null, 2));

    // Verify conversion was successful
    expect(conversionResult.success).toBe(true);

    // Verify formatting elements were converted correctly
    expect(conversionResult.original.bold).toBeGreaterThan(0);
    expect(conversionResult.original.italic).toBeGreaterThan(0);
    expect(conversionResult.original.strikethrough).toBeGreaterThan(0);
    expect(conversionResult.original.inlineCode).toBeGreaterThan(0);
    expect(conversionResult.original.heading).toBeGreaterThan(0);
    expect(conversionResult.original.table).toBeGreaterThan(0);
    expect(conversionResult.original.callout).toBeGreaterThan(0);

    // Verify Lexical conversion preserved formatting
    expect(conversionResult.lexical.bold).toBeGreaterThan(0);
    expect(conversionResult.lexical.italic).toBeGreaterThan(0);
    expect(conversionResult.lexical.strikethrough).toBeGreaterThan(0);
    expect(conversionResult.lexical.inlineCode).toBeGreaterThan(0);
    expect(conversionResult.lexical.heading).toBeGreaterThan(0);
    expect(conversionResult.lexical.table).toBeGreaterThan(0);
    expect(conversionResult.lexical.callout).toBeGreaterThan(0);

    // Verify round-trip conversion works
    expect(conversionResult.roundTrip.success).toBe(true);

    console.log("✅ All formatting elements converted successfully!");
    console.log(`📊 Conversion stats:
    - Bold: ${conversionResult.original.bold} → ${conversionResult.lexical.bold}
    - Italic: ${conversionResult.original.italic} → ${conversionResult.lexical.italic}
    - Strikethrough: ${conversionResult.original.strikethrough} → ${conversionResult.lexical.strikethrough}
    - Inline Code: ${conversionResult.original.inlineCode} → ${conversionResult.lexical.inlineCode}
    - Headings: ${conversionResult.original.heading} → ${conversionResult.lexical.heading}
    - Tables: ${conversionResult.original.table} → ${conversionResult.lexical.table}
    - Callouts: ${conversionResult.original.callout} → ${conversionResult.lexical.callout}`);
  });
});
