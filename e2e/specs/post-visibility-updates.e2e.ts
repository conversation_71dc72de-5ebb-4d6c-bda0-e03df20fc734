/**
 * E2E Tests for Post Visibility Updates
 *
 * This file tests the ability to update post visibility in Ghost through the plugin.
 * It covers:
 * - Updating post visibility from public to members
 * - Updating post visibility from members to paid
 * - Updating post visibility from paid back to public
 * - Verifying visibility changes are reflected in Ghost
 * - Verifying visibility changes are synced back to local files
 */

import { expect, test, beforeAll, afterAll, beforeEach, afterEach, describe, it } from 'vitest';
import { getSharedTestContext, resetSharedTestContext, cleanupTestState, type SharedTestContext } from '../helpers/shared-context';
import { waitForOperation, createTestFile, openFile } from '../helpers/electron-setup';';
import { resetObsidianUI, getSyncMetadata, verifyPluginAvailable } from '../helpers/plugin-setup';
import * as fs from 'fs';
import * as path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Helper to restore vault from pristine state
 */
async function restoreVaultFromPristine(): Promise<void> {
  const pristineVaultPath = path.join(__dirname, '../../tests/vault/Test.pristine');
  const testVaultPath = path.join(__dirname, '../../tests/vault/Test');

  if (fs.existsSync(testVaultPath)) {
    fs.rmSync(testVaultPath, { recursive: true, force: true });
  }

  fs.cpSync(pristineVaultPath, testVaultPath, { recursive: true });
  console.log('🔄 Restoring vault from pristine state...');
  console.log('✅ Vault restored from pristine state');
}

/**
 * Helper to copy plugin files to test vault
 */
async function copyPluginFiles(): Promise<void> {
  const projectRoot = path.join(__dirname, '../..');
  const pluginTargetPath = path.join(__dirname, '../../tests/vault/Test/.obsidian/plugins/ghost-sync');

  if (!fs.existsSync(pluginTargetPath)) {
    fs.mkdirSync(pluginTargetPath, { recursive: true });
  }

  console.log('📁 Copying plugin files to test vault...');

  // Copy the built files from project root
  const filesToCopy = ['main.js', 'manifest.json', 'styles.css'];
  for (const file of filesToCopy) {
    const sourcePath = path.join(projectRoot, file);
    const targetPath = path.join(pluginTargetPath, file);
    if (fs.existsSync(sourcePath)) {
      fs.copyFileSync(sourcePath, targetPath);
    }
  }

  console.log('✅ Plugin files copied');
}

describe('Ghost Sync - Post Visibility Updates E2E Tests', () => {
  let context: SharedTestContext;

  beforeAll(async () => {
    context = await getSharedTestContext();
  });

  // afterAll cleanup handled by global teardown

  beforeEach(async () => {
    await resetSharedTestContext();
  });

  afterEach(async () => {
    await resetSharedTestContext();
  });

  it('should update post visibility from public to members', async () => {
    // Create a test file with public visibility
    const testContent = `---
title: "Members Visibility Test Post"
slug: "members-visibility-test-post"
status: "draft"
visibility: "public"
---

# Members Visibility Test Post

This post will test updating visibility from public to members.

Content for testing visibility updates.`;

    const relativeFilePath = 'articles/members-visibility-test-post.md';

    await context.page.evaluate(async ({ content, path }) => {
      const file = await (window as any).app.vault.create(path, content);
      await (window as any).app.workspace.getLeaf().openFile(file);
    }, { content: testContent, path: relativeFilePath });

    await waitForOperation(1000);

    // Sync the visibility change to Ghost
    await context.page.keyboard.press('Meta+P');
    await waitForOperation(500);
    await context.page.keyboard.type('Sync current post to Ghost');
    await context.page.keyboard.press('Enter');

    await waitForOperation(3000);

    // Verify the sync was successful
    const notices = await context.page.evaluate(() => {
      const noticeElements = document.querySelectorAll('.notice');
      return Array.from(noticeElements).map(el => el.textContent?.trim() || '');
    });

    const hasSuccessNotice = notices.some(notice =>
      notice?.toLowerCase().includes('sync') ||
      notice?.toLowerCase().includes('success') ||
      notice?.toLowerCase().includes('updated')
    );

    console.log(`Visibility update notices: ${notices.join(', ')}`);
    expect(hasSuccessNotice).toBe(true);

    console.log(`✅ Successfully updated post visibility from public to members`);
  });

    it('should update post visibility from members to paid', async () => {
      // Create a test file with members visibility
      const testContent = `---
title: "Paid Visibility Test Post"
slug: "paid-visibility-test-post"
status: "draft"
visibility: "members"
---

# Paid Visibility Test Post

This post will test visibility updates from members to paid.

Content for testing paid visibility.`;

      const relativeFilePath = 'articles/paid-visibility-test-post.md';

      await context.page.evaluate(async ({ content, path }) => {
        const file = await (window as any).app.vault.create(path, content);
        await (window as any).app.workspace.getLeaf().openFile(file);
      }, { content: testContent, path: relativeFilePath });

      await waitForOperation(1000);

      // First sync to Ghost to create the post
      await context.page.keyboard.press('Meta+P');
      await waitForOperation(500);
      await context.page.keyboard.type('Sync current post to Ghost');
      await context.page.keyboard.press('Enter');

      await waitForOperation(3000);

      // Update the visibility to paid
      const updatedContent = testContent.replace(
        'visibility: "members"',
        'visibility: "paid"'
      );

      await context.page.evaluate(async ({ content, path }) => {
        const file = (window as any).app.vault.getAbstractFileByPath(path);
        if (file) {
          await (window as any).app.vault.modify(file, content);
        }
      }, { content: updatedContent, path: relativeFilePath });

      await waitForOperation(1000);

      // Sync the visibility change to Ghost
      await context.page.keyboard.press('Meta+P');
      await waitForOperation(500);
      await context.page.keyboard.type('Sync current post to Ghost');
      await context.page.keyboard.press('Enter');

      await waitForOperation(3000);

      // Verify the sync was successful
      const notices = await context.page.evaluate(() => {
        const noticeElements = document.querySelectorAll('.notice');
        return Array.from(noticeElements).map(el => el.textContent?.trim() || '');
      });

      const hasSuccessNotice = notices.some(notice =>
        notice?.toLowerCase().includes('sync') ||
        notice?.toLowerCase().includes('success') ||
        notice?.toLowerCase().includes('updated')
      );

      console.log(`Paid visibility update notices: ${notices.join(', ')}`);
      expect(hasSuccessNotice).toBe(true);

      console.log(`✅ Successfully updated post visibility from members to paid`);
    });

    it('should verify visibility changes are reflected in Ghost tab', async () => {
      // Create a test file with public visibility
      const testContent = `---
title: "Ghost Tab Visibility Test"
slug: "ghost-tab-visibility-test"
status: "draft"
visibility: "public"
---

# Ghost Tab Visibility Test

This post will test visibility display in Ghost tab.`;

      const relativeFilePath = 'articles/ghost-tab-visibility-test.md';

      await context.page.evaluate(async ({ content, path }) => {
        const file = await (window as any).app.vault.create(path, content);
        await (window as any).app.workspace.getLeaf().openFile(file);
      }, { content: testContent, path: relativeFilePath });

      await waitForOperation(1000);

      // Open Ghost tab to check visibility display
      await context.page.keyboard.press('Meta+P');
      await waitForOperation(500);
      await context.page.keyboard.type('Ghost: Open sync status');
      await context.page.keyboard.press('Enter');

      await waitForOperation(2000);

      // Check if visibility is displayed in the Ghost tab
      const visibilityDisplay = await context.page.evaluate(() => {
        // Look for any element containing "Visibility" text
        const allElements = document.querySelectorAll('*');
        for (let i = 0; i < allElements.length; i++) {
          const element = allElements[i];
          if (element.textContent?.includes('Visibility')) {
            return { found: true, value: element.textContent?.trim() };
          }
        }
        return { found: false, value: null };
      });

      console.log(`Visibility display in Ghost tab: ${JSON.stringify(visibilityDisplay)}`);
      expect(visibilityDisplay.found).toBe(true);

      console.log(`✅ Visibility is properly displayed in Ghost tab`);
    });

    it('should support scheduling posts via publish dialog', async () => {
      // Create a test file for scheduling
      const testContent = `---
title: "Scheduling Test Post"
slug: "scheduling-test-post"
status: "draft"
visibility: "public"
---

# Scheduling Test Post

This post will test the scheduling functionality.

Content for testing post scheduling.`;

      const relativeFilePath = 'articles/scheduling-test-post.md';

      await context.page.evaluate(async ({ content, path }) => {
        const file = await (window as any).app.vault.create(path, content);
        await (window as any).app.workspace.getLeaf().openFile(file);
      }, { content: testContent, path: relativeFilePath });

      await waitForOperation(1000);

      // First sync to Ghost to create the post
      await context.page.keyboard.press('Meta+P');
      await waitForOperation(500);
      await context.page.keyboard.type('Sync current post to Ghost');
      await context.page.keyboard.press('Enter');

      await waitForOperation(3000);

      // Open Ghost tab
      await context.page.keyboard.press('Meta+P');
      await waitForOperation(500);
      await context.page.keyboard.type('Ghost: Open sync status');
      await context.page.keyboard.press('Enter');

      await waitForOperation(2000);

      // Click the Publish button to open the publish dialog
      const publishButton = context.page.locator('.ghost-sync-buttons button:has-text("Publish")');
      await publishButton.click();

      await waitForOperation(1000);

      // Check if the publish dialog opened and has scheduling options
      const dialogCheck = await context.page.evaluate(() => {
        const dialog = document.querySelector('.ghost-publish-dialog');
        const schedulingSection = document.querySelector('.publish-dialog-section h3');
        const scheduleRadio = document.querySelector('input[value="scheduled"]');

        return {
          dialogExists: !!dialog,
          hasSchedulingSection: !!schedulingSection,
          hasScheduleRadio: !!scheduleRadio
        };
      });

      console.log(`Publish dialog check: ${JSON.stringify(dialogCheck)}`);
      expect(dialogCheck.dialogExists).toBe(true);

      // Close the dialog
      await context.page.keyboard.press('Escape');
      await waitForOperation(500);

      console.log(`✅ Scheduling functionality is available in publish dialog`);
    });
});
